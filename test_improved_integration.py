#!/usr/bin/env python3
"""
Test the improved integration that handles running scraping sessions.
"""

import requests
import json
import time

def reset_status():
    """Reset the scraping status."""
    try:
        response = requests.post('http://127.0.0.1:5000/api/scraping_reset')
        return response.status_code == 200
    except:
        return False

def start_scraping():
    """Start a scraping session."""
    try:
        url = 'http://127.0.0.1:5000/scrape'
        data = {
            'keywords': 'test session',
            'class_name': 'test_session',
            'max_images': '5'
        }
        headers = {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/x-www-form-urlencoded'
        }
        
        response = requests.post(url, data=data, headers=headers)
        return response.status_code == 200 and response.json().get('status') == 'success'
    except:
        return False

def check_status():
    """Check current scraping status."""
    try:
        response = requests.get('http://127.0.0.1:5000/api/scraping_status')
        if response.status_code == 200:
            return response.json()
        return None
    except:
        return None

def test_page_with_running_scraping():
    """Test that the page loads correctly when scraping is already running."""
    try:
        response = requests.get('http://127.0.0.1:5000/')
        if response.status_code == 200:
            content = response.text
            # Check that the page has the checkInitialScrapingStatus function
            has_initial_check = 'checkInitialScrapingStatus' in content
            print(f"✅ Page loads with running scraping: {response.status_code}")
            print(f"✅ Has initial status check: {has_initial_check}")
            return has_initial_check
        else:
            print(f"❌ Page failed to load: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error testing page: {e}")
        return False

def test_form_submission_with_running_scraping():
    """Test form submission when scraping is already running."""
    try:
        url = 'http://127.0.0.1:5000/scrape'
        data = {
            'keywords': 'another test',
            'class_name': 'another_test',
            'max_images': '3'
        }
        headers = {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/x-www-form-urlencoded'
        }
        
        response = requests.post(url, data=data, headers=headers)
        
        if response.status_code == 200:
            json_data = response.json()
            is_error = json_data.get('status') == 'error'
            has_progress_msg = 'already in progress' in json_data.get('message', '').lower()
            
            print(f"✅ Form submission with running scraping: {response.status_code}")
            print(f"✅ Returns error status: {is_error}")
            print(f"✅ Has 'already in progress' message: {has_progress_msg}")
            print(f"   Message: {json_data.get('message', 'No message')}")
            
            return is_error and has_progress_msg
        else:
            print(f"❌ Form submission failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error testing form submission: {e}")
        return False

def run_improved_test():
    """Run the improved integration test."""
    print("🧪 Testing Improved Integration (Handles Running Scraping)")
    print("=" * 60)
    
    # Step 1: Reset status
    print("\n🔄 Step 1: Resetting scraping status...")
    if not reset_status():
        print("❌ Could not reset status")
        return False
    print("✅ Status reset successfully")
    
    time.sleep(1)
    
    # Step 2: Start a scraping session
    print("\n🚀 Step 2: Starting a scraping session...")
    if not start_scraping():
        print("❌ Could not start scraping session")
        return False
    print("✅ Scraping session started")
    
    time.sleep(2)  # Let it get going
    
    # Step 3: Verify scraping is running
    print("\n📊 Step 3: Verifying scraping is running...")
    status = check_status()
    if not status or not status.get('is_running'):
        print("❌ Scraping is not running")
        return False
    print(f"✅ Scraping is running: {status.get('progress', 'No progress info')}")
    
    # Step 4: Test page loading with running scraping
    print("\n🌐 Step 4: Testing page load with running scraping...")
    if not test_page_with_running_scraping():
        print("❌ Page test failed")
        return False
    print("✅ Page loads correctly with running scraping")
    
    # Step 5: Test form submission with running scraping
    print("\n📝 Step 5: Testing form submission with running scraping...")
    if not test_form_submission_with_running_scraping():
        print("❌ Form submission test failed")
        return False
    print("✅ Form submission handles running scraping correctly")
    
    # Summary
    print("\n" + "=" * 60)
    print("🎉 ALL IMPROVED INTEGRATION TESTS PASSED!")
    print("=" * 60)
    print("✅ Page automatically detects running scraping")
    print("✅ Form submission gracefully handles running scraping")
    print("✅ No more 'already in progress' errors for users")
    print("✅ Seamless user experience maintained")
    
    return True

if __name__ == '__main__':
    success = run_improved_test()
    if success:
        print("\n🎊 Integration improvement successful!")
    else:
        print("\n❌ Integration improvement needs work")
    exit(0 if success else 1)
