{% extends "base.html" %}

{% block title %}Dashboard - Image Scraper{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-th-large me-2"></i>Image Management Dashboard</h2>
    <a href="{{ url_for('index') }}" class="btn btn-primary">
        <i class="fas fa-plus me-1"></i>Scrape More Images
    </a>
</div>

{% if classes %}
<!-- Tab Navigation -->
<div class="mb-4">
    <ul class="nav nav-tabs" id="categoryTabs" role="tablist">
        {% for group_name, group_classes in grouped_classes.items() %}
        <li class="nav-item" role="presentation">
            <button class="nav-link {% if loop.first %}active{% endif %}"
                id="{{ group_name|replace('#', 'alpha-')|replace(' ', '-')|lower }}-tab" data-bs-toggle="tab"
                data-bs-target="#{{ group_name|replace('#', 'alpha-')|replace(' ', '-')|lower }}-pane" type="button"
                role="tab">
                {% if group_name == 'All' %}
                <i class="fas fa-th-large me-1"></i>All
                <span class="badge bg-primary ms-1">{{ group_classes|length }}</span>
                {% elif group_name.startswith('#') %}
                <i class="fas fa-font me-1"></i>{{ group_name }}
                <span class="badge bg-secondary ms-1">{{ group_classes|length }}</span>
                {% else %}
                {% set icons = {
                'Animals': 'fa-paw',
                'Vehicles': 'fa-car',
                'Food': 'fa-utensils',
                'Nature': 'fa-leaf',
                'Objects': 'fa-cube',
                'People': 'fa-users',
                'Sports': 'fa-football-ball',
                'Technology': 'fa-microchip'
                } %}
                <i class="fas {{ icons.get(group_name, 'fa-folder') }} me-1"></i>{{ group_name }}
                <span class="badge bg-info ms-1">{{ group_classes|length }}</span>
                {% endif %}
            </button>
        </li>
        {% endfor %}
    </ul>
</div>

<!-- Tab Content -->
<div class="tab-content" id="categoryTabsContent">
    {% for group_name, group_classes in grouped_classes.items() %}
    <div class="tab-pane fade {% if loop.first %}show active{% endif %}"
        id="{{ group_name|replace('#', 'alpha-')|replace(' ', '-')|lower }}-pane" role="tabpanel">

        {% if group_classes %}
        <div class="row">
            {% for class in group_classes %}
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card h-100 shadow-sm class-card">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="fas fa-folder me-2 text-primary"></i>{{ class.name }}
                        </h5>
                        <p class="card-text">
                            <span class="badge bg-secondary">
                                <i class="fas fa-image me-1"></i>{{ class.image_count }} images
                            </span>
                        </p>
                        <p class="card-text">
                            <small class="text-muted">
                                <i class="fas fa-folder-open me-1"></i>{{ class.path }}
                            </small>
                        </p>
                    </div>
                    <div class="card-footer bg-transparent">
                        <div class="d-grid gap-2">
                            <a href="{{ url_for('view_images', class_name=class.name) }}"
                                class="btn btn-outline-primary">
                                <i class="fas fa-eye me-1"></i>View Images
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="text-center py-4">
            <div class="mb-3">
                <i class="fas fa-folder-open fa-3x text-muted"></i>
            </div>
            <h5 class="text-muted">No folders in this category</h5>
            <p class="text-muted">Try scraping some images to populate this category.</p>
        </div>
        {% endif %}
    </div>
    {% endfor %}
</div>

<div class="mt-4">
    <div class="card">
        <div class="card-body">
            <h5 class="card-title">
                <i class="fas fa-chart-bar me-2"></i>Summary
            </h5>
            <div class="row text-center">
                <div class="col-md-4">
                    <div class="border-end">
                        <h3 class="text-primary">{{ classes|length }}</h3>
                        <p class="text-muted mb-0">Total Classes</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="border-end">
                        <h3 class="text-success">{{ classes|sum(attribute='image_count') }}</h3>
                        <p class="text-muted mb-0">Total Images</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <h3 class="text-info">{{ "%.1f"|format(classes|sum(attribute='image_count') / classes|length) if
                        classes|length > 0 else 0 }}</h3>
                    <p class="text-muted mb-0">Avg per Class</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% else %}
<div class="text-center py-5">
    <div class="mb-4">
        <i class="fas fa-folder-open fa-5x text-muted"></i>
    </div>
    <h3 class="text-muted">No Image Classes Found</h3>
    <p class="text-muted mb-4">You haven't scraped any images yet. Start by creating your first image collection!</p>
    <a href="{{ url_for('index') }}" class="btn btn-primary btn-lg">
        <i class="fas fa-search me-2"></i>Start Scraping Images
    </a>
</div>
{% endif %}
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Add hover effects to class cards
        const classCards = document.querySelectorAll('.class-card');
        classCards.forEach(card => {
            card.addEventListener('mouseenter', function () {
                this.style.transform = 'translateY(-5px)';
                this.style.transition = 'transform 0.2s ease, box-shadow 0.2s ease';
                this.style.boxShadow = '0 8px 25px rgba(0,0,0,0.15)';
            });

            card.addEventListener('mouseleave', function () {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = '';
            });
        });

        // Add tab switching animation
        const tabButtons = document.querySelectorAll('[data-bs-toggle="tab"]');
        tabButtons.forEach(button => {
            button.addEventListener('shown.bs.tab', function (e) {
                const targetPane = document.querySelector(e.target.getAttribute('data-bs-target'));
                if (targetPane) {
                    targetPane.style.opacity = '0';
                    targetPane.style.transform = 'translateY(20px)';

                    setTimeout(() => {
                        targetPane.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
                        targetPane.style.opacity = '1';
                        targetPane.style.transform = 'translateY(0)';
                    }, 50);
                }
            });
        });

        // Search functionality within tabs
        function addSearchToTabs() {
            const searchContainer = document.createElement('div');
            searchContainer.className = 'mb-3';
            searchContainer.innerHTML = `
            <div class="input-group">
                <span class="input-group-text">
                    <i class="fas fa-search"></i>
                </span>
                <input type="text" class="form-control" id="folderSearch"
                       placeholder="Search folders..." autocomplete="off">
                <button class="btn btn-outline-secondary" type="button" id="clearSearch">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

            const tabContent = document.getElementById('categoryTabsContent');
            if (tabContent) {
                tabContent.parentNode.insertBefore(searchContainer, tabContent);
            }

            // Search functionality
            const searchInput = document.getElementById('folderSearch');
            const clearButton = document.getElementById('clearSearch');

            if (searchInput) {
                searchInput.addEventListener('input', function () {
                    const searchTerm = this.value.toLowerCase();
                    filterFolders(searchTerm);
                });
            }

            if (clearButton) {
                clearButton.addEventListener('click', function () {
                    searchInput.value = '';
                    filterFolders('');
                    searchInput.focus();
                });
            }
        }

        function filterFolders(searchTerm) {
            const allCards = document.querySelectorAll('.class-card');
            let visibleCount = 0;

            allCards.forEach(card => {
                const folderName = card.querySelector('.card-title').textContent.toLowerCase();
                const cardContainer = card.closest('.col-md-6');

                if (folderName.includes(searchTerm)) {
                    cardContainer.style.display = '';
                    visibleCount++;
                } else {
                    cardContainer.style.display = 'none';
                }
            });

            // Show/hide empty state for each tab
            const tabPanes = document.querySelectorAll('.tab-pane');
            tabPanes.forEach(pane => {
                const visibleCardsInPane = pane.querySelectorAll('.col-md-6:not([style*="display: none"])').length;
                let emptyState = pane.querySelector('.search-empty-state');

                if (searchTerm && visibleCardsInPane === 0) {
                    if (!emptyState) {
                        emptyState = document.createElement('div');
                        emptyState.className = 'text-center py-4 search-empty-state';
                        emptyState.innerHTML = `
                        <div class="mb-3">
                            <i class="fas fa-search fa-3x text-muted"></i>
                        </div>
                        <h5 class="text-muted">No folders found</h5>
                        <p class="text-muted">Try adjusting your search terms.</p>
                    `;
                        pane.appendChild(emptyState);
                    }
                    emptyState.style.display = 'block';
                } else if (emptyState) {
                    emptyState.style.display = 'none';
                }
            });
        }

        // Initialize search functionality
        addSearchToTabs();

        // Update tab badges when filtering
        function updateTabBadges() {
            const tabs = document.querySelectorAll('[data-bs-toggle="tab"]');
            tabs.forEach(tab => {
                const targetPaneId = tab.getAttribute('data-bs-target');
                const targetPane = document.querySelector(targetPaneId);
                if (targetPane) {
                    const visibleCards = targetPane.querySelectorAll('.col-md-6:not([style*="display: none"])').length;
                    const badge = tab.querySelector('.badge');
                    if (badge) {
                        const originalCount = badge.getAttribute('data-original-count') || badge.textContent;
                        if (!badge.getAttribute('data-original-count')) {
                            badge.setAttribute('data-original-count', badge.textContent);
                        }
                        badge.textContent = visibleCards;
                    }
                }
            });
        }

        // Update badges on search
        const searchInput = document.getElementById('folderSearch');
        if (searchInput) {
            searchInput.addEventListener('input', function () {
                setTimeout(updateTabBadges, 100);
            });
        }
    });
</script>

<style>
    .nav-tabs .nav-link {
        border-radius: 8px 8px 0 0;
        margin-right: 2px;
        transition: all 0.2s ease;
    }

    .nav-tabs .nav-link:hover {
        background-color: #f8f9fa;
        border-color: #dee2e6 #dee2e6 #fff;
    }

    .nav-tabs .nav-link.active {
        background-color: #fff;
        border-color: #dee2e6 #dee2e6 #fff;
        font-weight: 600;
    }

    .tab-content {
        border: 1px solid #dee2e6;
        border-top: none;
        border-radius: 0 0 8px 8px;
        padding: 1.5rem;
        background-color: #fff;
    }

    .class-card {
        transition: transform 0.2s ease, box-shadow 0.2s ease;
        border: 1px solid #e9ecef;
    }

    .class-card:hover {
        border-color: #007bff;
    }

    .badge {
        font-size: 0.75em;
    }

    .input-group .form-control:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .search-empty-state {
        background-color: #f8f9fa;
        border-radius: 8px;
        margin: 1rem 0;
    }

    @media (max-width: 768px) {
        .nav-tabs {
            flex-wrap: wrap;
        }

        .nav-tabs .nav-link {
            font-size: 0.875rem;
            padding: 0.5rem 0.75rem;
        }

        .tab-content {
            padding: 1rem;
        }
    }
</style>
{% endblock %}