<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test New Button Approach</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Test New Button Approach</h1>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>New Button Implementation</h5>
                    </div>
                    <div class="card-body">
                        <div id="scrapeForm">
                            <div class="mb-3">
                                <label for="keywords" class="form-label">Keywords *</label>
                                <input type="text" class="form-control" id="keywords" value="button test">
                                <div class="invalid-feedback">Please enter search keywords</div>
                            </div>
                            <div class="mb-3">
                                <label for="class_name" class="form-label">Class Name *</label>
                                <input type="text" class="form-control" id="class_name" value="button_test">
                                <div class="invalid-feedback">Please enter a class/category name</div>
                            </div>
                            <div class="mb-3">
                                <label for="destination_folder" class="form-label">Destination Folder</label>
                                <input type="text" class="form-control" id="destination_folder" value="">
                            </div>
                            <div class="mb-3">
                                <label for="max_images" class="form-label">Max Images</label>
                                <select class="form-select" id="max_images">
                                    <option value="3" selected>3 images</option>
                                    <option value="5">5 images</option>
                                </select>
                            </div>
                            <div class="d-grid gap-2">
                                <button type="button" class="btn btn-primary btn-lg" id="startScrapingBtn" onclick="startScraping()">
                                    <i class="fas fa-download me-2"></i>Start Scraping
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Debug Log</h5>
                    </div>
                    <div class="card-body">
                        <div id="debugLog" style="height: 400px; overflow-y: auto; background: #f8f9fa; padding: 10px; font-family: monospace; font-size: 12px;">
                            Ready for testing...<br>
                        </div>
                        <button class="btn btn-secondary btn-sm mt-2" onclick="clearLog()">Clear Log</button>
                        <button class="btn btn-info btn-sm mt-2" onclick="testValidation()">Test Validation</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function log(message) {
            const logDiv = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('debugLog').innerHTML = 'Log cleared...<br>';
        }
        
        function testValidation() {
            log('Testing validation with empty fields...');
            document.getElementById('keywords').value = '';
            document.getElementById('class_name').value = '';
            startScraping();
        }
        
        // New button approach implementation
        function startScraping() {
            log('Start scraping button clicked');

            // Validate form inputs
            if (!validateInputs()) {
                log('Input validation failed');
                return;
            }

            log('Input validation passed');

            // Get button and update UI
            const startBtn = document.getElementById('startScrapingBtn');
            if (startBtn) {
                startBtn.disabled = true;
                startBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Starting...';
            }

            // Collect form data
            const formData = collectFormData();
            log('Form data collected: ' + JSON.stringify(formData));

            // Send AJAX request
            sendScrapingRequest(formData);
        }

        function validateInputs() {
            const keywords = document.getElementById('keywords').value.trim();
            const className = document.getElementById('class_name').value.trim();
            
            // Clear previous validation
            clearValidationErrors();

            let isValid = true;

            if (!keywords) {
                showValidationError('keywords', 'Please enter search keywords');
                isValid = false;
            }

            if (!className) {
                showValidationError('class_name', 'Please enter a class/category name');
                isValid = false;
            }

            return isValid;
        }

        function collectFormData() {
            return {
                keywords: document.getElementById('keywords').value.trim(),
                class_name: document.getElementById('class_name').value.trim(),
                destination_folder: document.getElementById('destination_folder').value.trim(),
                max_images: document.getElementById('max_images').value
            };
        }

        function sendScrapingRequest(formData) {
            log('Sending scraping request...');

            // Convert to URLSearchParams for proper form encoding
            const params = new URLSearchParams();
            Object.keys(formData).forEach(key => {
                params.append(key, formData[key]);
            });

            fetch('http://127.0.0.1:5000/scrape', {
                method: 'POST',
                body: params,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            })
            .then(response => {
                log(`Response received: ${response.status} ${response.statusText}`);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                log(`JSON response: ${JSON.stringify(data)}`);
                handleScrapingResponse(data);
            })
            .catch(error => {
                log(`Request failed: ${error.message}`);
                handleScrapingError(error.message);
            });
        }

        function handleScrapingResponse(data) {
            if (data.status === 'success') {
                log('✅ Scraping started successfully!');
                log('NEW BUTTON APPROACH IS WORKING!');
            } else {
                log(`❌ Scraping failed: ${data.message}`);
                handleScrapingError(data.message || 'Unknown error occurred');
            }
        }

        function handleScrapingError(errorMessage) {
            log(`Error: ${errorMessage}`);
            resetStartButton();
        }

        function resetStartButton() {
            const startBtn = document.getElementById('startScrapingBtn');
            if (startBtn) {
                startBtn.disabled = false;
                startBtn.innerHTML = '<i class="fas fa-download me-2"></i>Start Scraping';
            }
        }

        function showValidationError(fieldId, message) {
            const field = document.getElementById(fieldId);
            if (field) {
                field.classList.add('is-invalid');
                
                // Find error message element
                let errorElement = field.parentNode.querySelector('.invalid-feedback');
                if (errorElement) {
                    errorElement.textContent = message;
                }
            }
            log(`Validation error for ${fieldId}: ${message}`);
        }

        function clearValidationErrors() {
            const fields = ['keywords', 'class_name'];
            fields.forEach(fieldId => {
                const field = document.getElementById(fieldId);
                if (field) {
                    field.classList.remove('is-invalid');
                }
            });
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            log('DOM loaded - new button approach ready');
        });
    </script>
</body>
</html>
