<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frontend Integration Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .log-display {
            height: 400px;
            overflow-y: auto;
            background-color: #1e1e1e;
            color: #ffffff;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            padding: 1rem;
            border-radius: 0 0 0.375rem 0.375rem;
        }
        .log-entry {
            margin-bottom: 0.25rem;
            padding: 0.25rem 0;
            border-left: 3px solid transparent;
            padding-left: 0.5rem;
        }
        .log-entry.log-info {
            border-left-color: #17a2b8;
            color: #b3d9ff;
        }
        .log-entry.log-success {
            border-left-color: #28a745;
            color: #b3ffb3;
        }
        .log-entry.log-warning {
            border-left-color: #ffc107;
            color: #fff3cd;
        }
        .log-entry.log-error {
            border-left-color: #dc3545;
            color: #ffb3b3;
        }
        .log-entry.log-debug {
            border-left-color: #6c757d;
            color: #d1d1d1;
        }
        .log-timestamp {
            color: #6c757d;
            font-size: 0.8rem;
        }
        .log-level {
            font-weight: bold;
            margin-right: 0.5rem;
        }
        .log-message {
            word-wrap: break-word;
        }
        .log-display::-webkit-scrollbar {
            width: 8px;
        }
        .log-display::-webkit-scrollbar-track {
            background: #2d2d2d;
        }
        .log-display::-webkit-scrollbar-thumb {
            background: #555;
            border-radius: 4px;
        }
        .log-display::-webkit-scrollbar-thumb:hover {
            background: #777;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>Frontend Integration Test</h1>
        
        <!-- Form Section -->
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6" id="formSection">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h4 class="card-title mb-0">
                            <i class="fas fa-search me-2"></i>Test Form
                        </h4>
                    </div>
                    <div class="card-body">
                        <form id="scrapeForm" class="needs-validation" novalidate>
                            <div class="mb-3">
                                <label for="keywords" class="form-label">Keywords *</label>
                                <input type="text" class="form-control" id="keywords" name="keywords" 
                                       value="test frontend" required>
                            </div>
                            <div class="mb-3">
                                <label for="class_name" class="form-label">Class Name *</label>
                                <input type="text" class="form-control" id="class_name" name="class_name" 
                                       value="frontend_test" required>
                            </div>
                            <div class="mb-3">
                                <label for="max_images" class="form-label">Max Images</label>
                                <select class="form-select" id="max_images" name="max_images">
                                    <option value="5" selected>5 images</option>
                                    <option value="10">10 images</option>
                                </select>
                            </div>
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary btn-lg" id="submitBtn">
                                    <i class="fas fa-download me-2"></i>Start Test
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Progress Section (Initially Hidden) -->
            <div class="col-md-10" id="progressSection" style="display: none;">
                <div class="card shadow">
                    <div class="card-header bg-info text-white">
                        <h4 class="card-title mb-0">
                            <i class="fas fa-cog fa-spin me-2"></i>Test in Progress
                        </h4>
                    </div>
                    <div class="card-body">
                        <div class="text-center mb-4">
                            <div class="spinner-border text-primary" role="status" id="loadingSpinner">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>

                        <div class="progress mb-3" style="height: 25px;">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar"
                                style="width: 0%" id="progressBar">
                                <span id="progressText">Initializing...</span>
                            </div>
                        </div>

                        <div class="alert alert-info" role="alert" id="statusMessage">
                            <i class="fas fa-info-circle me-2"></i>
                            <span id="statusText">Preparing to start test...</span>
                        </div>

                        <!-- Real-time Logs Section -->
                        <div class="card mt-4">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">
                                    <i class="fas fa-terminal me-2"></i>Real-time Logs
                                </h6>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-secondary" id="toggleLogs" onclick="toggleLogDisplay()">
                                        <i class="fas fa-eye me-1"></i>Show Details
                                    </button>
                                    <button class="btn btn-outline-primary" id="downloadLogs" onclick="downloadLogs()" disabled>
                                        <i class="fas fa-download me-1"></i>Download
                                    </button>
                                    <button class="btn btn-outline-danger" id="clearLogs" onclick="clearLogs()" disabled>
                                        <i class="fas fa-trash me-1"></i>Clear
                                    </button>
                                </div>
                            </div>
                            <div class="card-body p-0" id="logsContainer" style="display: none;">
                                <div class="log-display" id="logDisplay">
                                    <div class="text-muted text-center p-3">
                                        <i class="fas fa-clock me-2"></i>Waiting for logs...
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="text-center" id="completionActions" style="display: none;">
                            <div class="mb-3">
                                <i class="fas fa-check-circle fa-3x text-success"></i>
                            </div>
                            <h5 class="text-success">Test Completed!</h5>
                            <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                                <button class="btn btn-outline-primary" onclick="resetToForm()">
                                    <i class="fas fa-search me-1"></i>Run Another Test
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let progressInterval;
        let logEventSource;
        let isCompleted = false;
        let logsVisible = false;

        // Form validation and AJAX submission
        document.addEventListener('DOMContentLoaded', function () {
            console.log('DOM loaded, setting up form validation and AJAX submission');

            const form = document.getElementById('scrapeForm');

            if (form) {
                console.log('Form found, attaching submit handler');

                form.addEventListener('submit', function (event) {
                    event.preventDefault(); // Prevent default form submission
                    console.log('Form submit triggered');

                    if (!form.checkValidity()) {
                        console.log('Form validation failed');
                        form.classList.add('was-validated');
                        return;
                    }

                    console.log('Form validation passed, submitting via AJAX');

                    // Disable submit button to prevent double submission
                    const submitBtn = document.getElementById('submitBtn');
                    if (submitBtn) {
                        submitBtn.disabled = true;
                        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
                    }

                    // Prepare form data
                    const formData = new FormData(form);

                    // Submit form via AJAX
                    fetch('http://127.0.0.1:5000/scrape', {
                        method: 'POST',
                        body: formData,
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    })
                        .then(response => response.json())
                        .then(data => {
                            console.log('AJAX response:', data);
                            if (data.status === 'success') {
                                // Show progress section and hide form
                                showProgressSection();
                                // Start monitoring progress
                                startProgressMonitoring();
                            } else {
                                // Show error message
                                alert('Error: ' + (data.message || 'Unknown error occurred'));
                                // Re-enable submit button
                                if (submitBtn) {
                                    submitBtn.disabled = false;
                                    submitBtn.innerHTML = '<i class="fas fa-download me-2"></i>Start Test';
                                }
                            }
                        })
                        .catch(error => {
                            console.error('Error submitting form:', error);
                            alert('Error submitting form. Please try again.');
                            // Re-enable submit button
                            if (submitBtn) {
                                submitBtn.disabled = false;
                                submitBtn.innerHTML = '<i class="fas fa-download me-2"></i>Start Test';
                            }
                        });

                    form.classList.add('was-validated');
                });
            } else {
                console.error('Form not found!');
            }
        });

        function showProgressSection() {
            console.log('Showing progress section');
            document.getElementById('formSection').style.display = 'none';
            document.getElementById('progressSection').style.display = 'block';
        }

        function resetToForm() {
            console.log('Resetting to form');
            // Reset form
            const form = document.getElementById('scrapeForm');
            if (form) {
                form.reset();
                form.classList.remove('was-validated');
            }

            // Reset submit button
            const submitBtn = document.getElementById('submitBtn');
            if (submitBtn) {
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fas fa-download me-2"></i>Start Test';
            }

            // Reset progress variables
            isCompleted = false;
            logsVisible = false;

            // Close log stream if open
            if (logEventSource) {
                logEventSource.close();
                logEventSource = null;
            }

            // Clear progress interval
            if (progressInterval) {
                clearInterval(progressInterval);
            }

            // Reset progress display
            resetProgressDisplay();

            // Show form section and hide progress
            document.getElementById('progressSection').style.display = 'none';
            document.getElementById('formSection').style.display = 'block';
        }

        function resetProgressDisplay() {
            // Reset progress bar
            const progressBar = document.getElementById('progressBar');
            const progressText = document.getElementById('progressText');
            if (progressBar && progressText) {
                progressBar.style.width = '0%';
                progressBar.className = 'progress-bar progress-bar-striped progress-bar-animated';
                progressText.textContent = 'Initializing...';
            }

            // Reset status message
            const statusMessage = document.getElementById('statusMessage');
            const statusText = document.getElementById('statusText');
            if (statusMessage && statusText) {
                statusMessage.className = 'alert alert-info';
                statusText.textContent = 'Preparing to start test...';
            }

            // Reset spinner
            const loadingSpinner = document.getElementById('loadingSpinner');
            if (loadingSpinner) {
                loadingSpinner.style.display = 'block';
            }

            // Hide completion actions
            const completionActions = document.getElementById('completionActions');
            if (completionActions) {
                completionActions.style.display = 'none';
            }

            // Reset logs
            const logDisplay = document.getElementById('logDisplay');
            const logsContainer = document.getElementById('logsContainer');
            const toggleButton = document.getElementById('toggleLogs');
            if (logDisplay && logsContainer && toggleButton) {
                logDisplay.innerHTML = '<div class="text-muted text-center p-3"><i class="fas fa-clock me-2"></i>Waiting for logs...</div>';
                logsContainer.style.display = 'none';
                toggleButton.innerHTML = '<i class="fas fa-eye me-1"></i>Show Details';
            }

            // Reset log controls
            const downloadLogs = document.getElementById('downloadLogs');
            const clearLogs = document.getElementById('clearLogs');
            if (downloadLogs) downloadLogs.disabled = true;
            if (clearLogs) clearLogs.disabled = true;
        }

        function startProgressMonitoring() {
            console.log('Starting progress monitoring');
            updateProgress(); // Initial update
            progressInterval = setInterval(updateProgress, 2000); // Update every 2 seconds
        }

        function updateProgress() {
            fetch('http://127.0.0.1:5000/api/scraping_status')
                .then(response => response.json())
                .then(data => {
                    console.log('Progress update:', data);
                    const statusText = document.getElementById('statusText');
                    const progressBar = document.getElementById('progressBar');
                    const progressText = document.getElementById('progressText');
                    const loadingSpinner = document.getElementById('loadingSpinner');
                    const completionActions = document.getElementById('completionActions');
                    const statusMessage = document.getElementById('statusMessage');

                    if (statusText) {
                        statusText.textContent = data.progress || 'Waiting...';

                        if (data.is_running) {
                            // Still running
                            progressBar.style.width = '50%';
                            progressText.textContent = 'In Progress...';
                            statusMessage.className = 'alert alert-info';
                            // Enable log controls
                            document.getElementById('downloadLogs').disabled = false;
                            document.getElementById('clearLogs').disabled = false;
                        } else if (data.progress && data.progress.includes('completed')) {
                            // Successfully completed
                            progressBar.style.width = '100%';
                            progressBar.className = 'progress-bar bg-success';
                            progressText.textContent = 'Complete!';
                            statusMessage.className = 'alert alert-success';

                            if (!isCompleted) {
                                isCompleted = true;
                                loadingSpinner.style.display = 'none';
                                completionActions.style.display = 'block';
                                clearInterval(progressInterval);
                                // Keep log controls enabled
                                document.getElementById('downloadLogs').disabled = false;
                            }
                        } else if (data.progress && data.progress.includes('Error')) {
                            // Error occurred
                            progressBar.style.width = '100%';
                            progressBar.className = 'progress-bar bg-danger';
                            progressText.textContent = 'Error';
                            statusMessage.className = 'alert alert-danger';
                            loadingSpinner.style.display = 'none';
                            clearInterval(progressInterval);

                            // Keep log controls enabled
                            document.getElementById('downloadLogs').disabled = false;
                        }
                    }
                })
                .catch(error => {
                    console.error('Error fetching status:', error);
                });
        }

        function toggleLogDisplay() {
            const logsContainer = document.getElementById('logsContainer');
            const toggleButton = document.getElementById('toggleLogs');

            if (logsVisible) {
                logsContainer.style.display = 'none';
                toggleButton.innerHTML = '<i class="fas fa-eye me-1"></i>Show Details';
                logsVisible = false;
            } else {
                logsContainer.style.display = 'block';
                toggleButton.innerHTML = '<i class="fas fa-eye-slash me-1"></i>Hide Details';
                logsVisible = true;
            }
        }

        function downloadLogs() {
            window.open('http://127.0.0.1:5000/api/scraping_logs/download', '_blank');
        }

        function clearLogs() {
            if (confirm('Are you sure you want to clear all logs?')) {
                fetch('http://127.0.0.1:5000/api/scraping_logs/clear', { method: 'POST' })
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'success') {
                            const logDisplay = document.getElementById('logDisplay');
                            logDisplay.innerHTML = '<div class="text-muted text-center p-3"><i class="fas fa-clock me-2"></i>Logs cleared...</div>';
                        }
                    })
                    .catch(error => {
                        console.error('Error clearing logs:', error);
                    });
            }
        }

        // Clean up when page is unloaded
        window.addEventListener('beforeunload', function () {
            if (progressInterval) {
                clearInterval(progressInterval);
            }
            if (logEventSource) {
                logEventSource.close();
            }
        });
    </script>
</body>
</html>
