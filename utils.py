import os
import shutil
from PIL import Image
import hashlib
from config import Config

def create_class_folder(base_path, class_name):
    """Create a folder for the specified class within the scraped_images directory.

    Note: base_path parameter is kept for backward compatibility but is ignored.
    All class folders are now created within the scraped_images directory.
    """
    # Always ensure the class folder is created within scraped_images
    # Use Config.UPLOAD_FOLDER for consistency
    scraped_images_dir = Config.UPLOAD_FOLDER

    # Create scraped_images directory if it doesn't exist
    if not os.path.exists(scraped_images_dir):
        os.makedirs(scraped_images_dir)

    # Create the class folder within scraped_images
    class_path = os.path.join(scraped_images_dir, class_name)
    if not os.path.exists(class_path):
        os.makedirs(class_path)
    return class_path

def get_all_classes(base_path):
    """Get all class folders and their image counts."""
    classes = []
    if not os.path.exists(base_path):
        return classes

    for item in os.listdir(base_path):
        item_path = os.path.join(base_path, item)
        if os.path.isdir(item_path):
            image_count = count_images_in_folder(item_path)
            classes.append({
                'name': item,
                'path': item_path,
                'image_count': image_count
            })
    return classes

def get_grouped_classes(base_path):
    """Get all classes grouped by categories for dashboard tabs."""
    classes = get_all_classes(base_path)
    if not classes:
        return {}

    # Define category keywords for grouping
    category_keywords = {
        'Animals': ['cat', 'dog', 'bird', 'fish', 'animal', 'pet', 'wildlife', 'horse', 'cow', 'sheep', 'pig', 'chicken', 'duck', 'rabbit', 'mouse', 'rat', 'elephant', 'lion', 'tiger', 'bear', 'wolf', 'fox', 'deer', 'monkey', 'ape', 'snake', 'lizard', 'frog', 'turtle', 'shark', 'whale', 'dolphin', 'penguin', 'eagle', 'owl', 'parrot', 'butterfly', 'bee', 'spider', 'ant'],
        'Vehicles': ['car', 'truck', 'bus', 'bike', 'motorcycle', 'bicycle', 'vehicle', 'auto', 'plane', 'airplane', 'helicopter', 'boat', 'ship', 'train', 'subway', 'taxi', 'van', 'suv', 'sedan', 'coupe', 'convertible', 'jeep', 'scooter', 'skateboard'],
        'Food': ['food', 'pizza', 'burger', 'sandwich', 'salad', 'fruit', 'apple', 'banana', 'orange', 'grape', 'strawberry', 'cake', 'bread', 'pasta', 'rice', 'meat', 'chicken', 'beef', 'pork', 'fish', 'seafood', 'vegetable', 'tomato', 'potato', 'carrot', 'onion', 'cheese', 'milk', 'coffee', 'tea', 'juice', 'water', 'wine', 'beer', 'dessert', 'chocolate', 'candy', 'cookie'],
        'Nature': ['tree', 'flower', 'plant', 'garden', 'forest', 'mountain', 'river', 'lake', 'ocean', 'beach', 'sky', 'cloud', 'sun', 'moon', 'star', 'nature', 'landscape', 'sunset', 'sunrise', 'grass', 'leaf', 'branch', 'rock', 'stone', 'sand', 'snow', 'rain', 'storm', 'rainbow'],
        'Objects': ['chair', 'table', 'book', 'phone', 'computer', 'laptop', 'keyboard', 'mouse', 'monitor', 'camera', 'watch', 'glasses', 'bag', 'shoe', 'clothes', 'shirt', 'pants', 'dress', 'hat', 'toy', 'ball', 'game', 'tool', 'hammer', 'screwdriver', 'knife', 'fork', 'spoon', 'plate', 'cup', 'bottle', 'box', 'key', 'lock', 'door', 'window', 'mirror', 'lamp', 'clock'],
        'People': ['person', 'people', 'man', 'woman', 'child', 'baby', 'boy', 'girl', 'family', 'couple', 'friend', 'student', 'teacher', 'doctor', 'nurse', 'worker', 'business', 'professional', 'athlete', 'artist', 'musician', 'actor', 'model', 'face', 'portrait', 'smile', 'happy', 'sad', 'angry'],
        'Sports': ['sport', 'football', 'soccer', 'basketball', 'baseball', 'tennis', 'golf', 'swimming', 'running', 'cycling', 'skiing', 'snowboarding', 'surfing', 'climbing', 'hiking', 'gym', 'fitness', 'exercise', 'yoga', 'dance', 'boxing', 'wrestling', 'hockey', 'volleyball', 'badminton', 'cricket', 'rugby'],
        'Technology': ['technology', 'tech', 'computer', 'laptop', 'smartphone', 'tablet', 'software', 'hardware', 'internet', 'web', 'app', 'digital', 'electronic', 'robot', 'ai', 'artificial', 'intelligence', 'code', 'programming', 'data', 'network', 'server', 'database', 'cloud', 'cyber', 'virtual', 'augmented', 'reality']
    }

    # Initialize groups
    groups = {'All': classes}

    # Group classes by categories
    for category, keywords in category_keywords.items():
        category_classes = []
        for class_item in classes:
            class_name_lower = class_item['name'].lower()
            if any(keyword in class_name_lower for keyword in keywords):
                category_classes.append(class_item)

        if category_classes:  # Only add category if it has classes
            groups[category] = category_classes

    # Group remaining classes alphabetically
    categorized_names = set()
    for category, category_classes in groups.items():
        if category != 'All':
            categorized_names.update(class_item['name'] for class_item in category_classes)

    uncategorized_classes = [c for c in classes if c['name'] not in categorized_names]

    # Create alphabetical groups for uncategorized classes
    alpha_groups = {}
    for class_item in uncategorized_classes:
        first_letter = class_item['name'][0].upper()
        if first_letter not in alpha_groups:
            alpha_groups[first_letter] = []
        alpha_groups[first_letter].append(class_item)

    # Add alphabetical groups to main groups
    for letter in sorted(alpha_groups.keys()):
        groups[f"#{letter}"] = alpha_groups[letter]

    return groups

def count_images_in_folder(folder_path):
    """Count the number of image files in a folder."""
    if not os.path.exists(folder_path):
        return 0

    count = 0
    for file in os.listdir(folder_path):
        if file.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.webp')):
            count += 1
    return count

def get_images_in_class(base_path, class_name):
    """Get all images in a specific class folder."""
    class_path = os.path.join(base_path, class_name)
    images = []

    if not os.path.exists(class_path):
        return images

    for file in os.listdir(class_path):
        if file.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.webp')):
            images.append({
                'filename': file,
                'path': os.path.join(class_path, file),
                'relative_path': os.path.join(class_name, file).replace('\\', '/')
            })
    return images

def delete_image(base_path, class_name, filename):
    """Delete a specific image file."""
    file_path = os.path.join(base_path, class_name, filename)
    if os.path.exists(file_path):
        os.remove(file_path)
        return True
    return False

def validate_image(file_path):
    """Validate if the file is a valid image."""
    try:
        with Image.open(file_path) as img:
            img.verify()
        return True
    except:
        return False

def generate_unique_filename(original_filename, folder_path):
    """Generate a unique filename to avoid conflicts."""
    name, ext = os.path.splitext(original_filename)
    counter = 1
    new_filename = original_filename

    while os.path.exists(os.path.join(folder_path, new_filename)):
        new_filename = f"{name}_{counter}{ext}"
        counter += 1

    return new_filename
