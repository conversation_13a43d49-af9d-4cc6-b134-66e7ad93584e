#!/usr/bin/env python3
"""
Reset scraping status and test the integration.
"""

import requests
import json
import time

def reset_status():
    """Reset the scraping status."""
    try:
        response = requests.post('http://127.0.0.1:5000/api/scraping_reset')
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Status reset: {data['message']}")
            return True
        else:
            print(f"❌ Failed to reset status: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error resetting status: {e}")
        return False

def check_status():
    """Check current scraping status."""
    try:
        response = requests.get('http://127.0.0.1:5000/api/scraping_status')
        if response.status_code == 200:
            data = response.json()
            print(f"Current status: {json.dumps(data, indent=2)}")
            return data
        else:
            print(f"❌ Failed to get status: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ Error getting status: {e}")
        return None

def test_ajax_form():
    """Test AJAX form submission."""
    url = 'http://127.0.0.1:5000/scrape'
    
    data = {
        'keywords': 'test integration',
        'class_name': 'integration_test',
        'max_images': '5'
    }
    
    headers = {
        'X-Requested-With': 'XMLHttpRequest',
        'Content-Type': 'application/x-www-form-urlencoded'
    }
    
    try:
        print("\n🧪 Testing AJAX form submission...")
        response = requests.post(url, data=data, headers=headers)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            json_data = response.json()
            print(f"Response: {json.dumps(json_data, indent=2)}")
            
            if json_data.get('status') == 'success':
                print("✅ AJAX form submission successful!")
                
                # Wait a moment and check status
                print("\n⏳ Waiting 3 seconds to check progress...")
                time.sleep(3)
                
                status = check_status()
                if status and status.get('is_running'):
                    print("✅ Scraping is now running!")
                else:
                    print("❌ Scraping doesn't appear to be running")
                
                return True
            else:
                print(f"❌ AJAX submission failed: {json_data.get('message')}")
                return False
        else:
            print(f"❌ HTTP error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing AJAX: {e}")
        return False

if __name__ == '__main__':
    print("🔄 Resetting and testing integration...")
    
    # Reset status first
    if reset_status():
        print("\n📊 Checking initial status...")
        check_status()
        
        # Test AJAX form
        test_ajax_form()
    else:
        print("❌ Could not reset status, aborting test")
    
    print("\n✅ Test completed!")
