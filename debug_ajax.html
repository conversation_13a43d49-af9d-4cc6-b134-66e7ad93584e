<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug AJAX</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Debug AJAX Request</h1>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Test Form</h5>
                    </div>
                    <div class="card-body">
                        <form id="testForm">
                            <div class="mb-3">
                                <label for="keywords" class="form-label">Keywords</label>
                                <input type="text" class="form-control" id="keywords" name="keywords" value="debug test" required>
                            </div>
                            <div class="mb-3">
                                <label for="class_name" class="form-label">Class Name</label>
                                <input type="text" class="form-control" id="class_name" name="class_name" value="debug_test" required>
                            </div>
                            <div class="mb-3">
                                <label for="max_images" class="form-label">Max Images</label>
                                <select class="form-select" id="max_images" name="max_images">
                                    <option value="3" selected>3 images</option>
                                </select>
                            </div>
                            <button type="submit" class="btn btn-primary" id="submitBtn">Test Submit</button>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Debug Log</h5>
                    </div>
                    <div class="card-body">
                        <div id="debugLog" style="height: 400px; overflow-y: auto; background: #f8f9fa; padding: 10px; font-family: monospace; font-size: 12px;">
                            Ready for testing...<br>
                        </div>
                        <button class="btn btn-secondary btn-sm mt-2" onclick="clearLog()">Clear Log</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function log(message) {
            const logDiv = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('debugLog').innerHTML = 'Log cleared...<br>';
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            log('DOM loaded');
            
            const form = document.getElementById('testForm');
            if (form) {
                log('Form found, attaching event listener');
                
                form.addEventListener('submit', function(event) {
                    event.preventDefault();
                    log('Form submit triggered');
                    
                    const submitBtn = document.getElementById('submitBtn');
                    submitBtn.disabled = true;
                    submitBtn.textContent = 'Processing...';
                    
                    const formData = new FormData(form);
                    log('Form data prepared');
                    
                    // Log form data contents
                    for (let [key, value] of formData.entries()) {
                        log(`Form data: ${key} = ${value}`);
                    }
                    
                    const url = 'http://127.0.0.1:5000/scrape';
                    log(`Submitting to: ${url}`);
                    
                    fetch(url, {
                        method: 'POST',
                        body: formData,
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    })
                    .then(response => {
                        log(`Response status: ${response.status} ${response.statusText}`);
                        log(`Response headers: ${JSON.stringify([...response.headers.entries()])}`);
                        
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }
                        
                        return response.text(); // Get as text first to see what we're getting
                    })
                    .then(text => {
                        log(`Raw response text: ${text}`);
                        
                        try {
                            const data = JSON.parse(text);
                            log(`Parsed JSON: ${JSON.stringify(data, null, 2)}`);
                            
                            if (data.status === 'success') {
                                log('✅ SUCCESS: Form submission worked!');
                            } else {
                                log(`❌ ERROR: ${data.message}`);
                            }
                        } catch (e) {
                            log(`❌ JSON Parse Error: ${e.message}`);
                            log(`Response was not valid JSON: ${text}`);
                        }
                        
                        // Re-enable button
                        submitBtn.disabled = false;
                        submitBtn.textContent = 'Test Submit';
                    })
                    .catch(error => {
                        log(`❌ Fetch Error: ${error.message}`);
                        log(`Error details: ${error.stack}`);
                        
                        // Re-enable button
                        submitBtn.disabled = false;
                        submitBtn.textContent = 'Test Submit';
                    });
                });
            } else {
                log('❌ Form not found!');
            }
        });
    </script>
</body>
</html>
