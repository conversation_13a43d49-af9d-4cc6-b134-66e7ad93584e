<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Form Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Simple Form Test</h1>
        
        <!-- Form Section -->
        <div id="formSection" class="card">
            <div class="card-header">
                <h5>Test Form</h5>
            </div>
            <div class="card-body">
                <form id="scrapeForm">
                    <div class="mb-3">
                        <label for="keywords" class="form-label">Keywords</label>
                        <input type="text" class="form-control" id="keywords" name="keywords" value="simple test" required>
                    </div>
                    <div class="mb-3">
                        <label for="class_name" class="form-label">Class Name</label>
                        <input type="text" class="form-control" id="class_name" name="class_name" value="simple_test" required>
                    </div>
                    <div class="mb-3">
                        <label for="max_images" class="form-label">Max Images</label>
                        <select class="form-select" id="max_images" name="max_images">
                            <option value="3" selected>3 images</option>
                        </select>
                    </div>
                    <button type="submit" class="btn btn-primary" id="submitBtn">
                        <i class="fas fa-download me-2"></i>Start Scraping
                    </button>
                </form>
            </div>
        </div>
        
        <!-- Progress Section (Initially Hidden) -->
        <div id="progressSection" style="display: none;" class="card mt-4">
            <div class="card-header bg-success text-white">
                <h5>Progress Section</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-success">
                    <h4>Success!</h4>
                    <p>Form was submitted successfully and this section is now visible.</p>
                    <button class="btn btn-outline-success" onclick="resetToForm()">Back to Form</button>
                </div>
            </div>
        </div>
        
        <!-- Debug Log -->
        <div class="card mt-4">
            <div class="card-header">
                <h6>Debug Log</h6>
            </div>
            <div class="card-body">
                <div id="debugLog" style="height: 200px; overflow-y: auto; background: #f8f9fa; padding: 10px; font-family: monospace; font-size: 12px;">
                    Ready...<br>
                </div>
            </div>
        </div>
    </div>

    <script>
        function log(message) {
            const logDiv = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function showProgressSection() {
            log('showProgressSection() called');
            const formSection = document.getElementById('formSection');
            const progressSection = document.getElementById('progressSection');
            
            if (formSection && progressSection) {
                log('Both sections found, hiding form and showing progress');
                formSection.style.display = 'none';
                progressSection.style.display = 'block';
                log('Section visibility changed successfully');
            } else {
                log(`ERROR: formSection=${!!formSection}, progressSection=${!!progressSection}`);
            }
        }
        
        function resetToForm() {
            log('resetToForm() called');
            const formSection = document.getElementById('formSection');
            const progressSection = document.getElementById('progressSection');
            const submitBtn = document.getElementById('submitBtn');
            
            if (formSection && progressSection) {
                formSection.style.display = 'block';
                progressSection.style.display = 'none';
                log('Reset to form view');
            }
            
            if (submitBtn) {
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fas fa-download me-2"></i>Start Scraping';
                log('Submit button reset');
            }
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            log('DOM loaded');
            
            const form = document.getElementById('scrapeForm');
            if (form) {
                log('Form found, attaching event listener');
                
                form.addEventListener('submit', function(event) {
                    event.preventDefault();
                    log('Form submit triggered');
                    
                    const submitBtn = document.getElementById('submitBtn');
                    if (submitBtn) {
                        submitBtn.disabled = true;
                        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
                        log('Submit button disabled and spinner shown');
                    }
                    
                    const formData = new FormData(form);
                    log('Form data prepared');
                    
                    // Log form data
                    for (let [key, value] of formData.entries()) {
                        log(`Form data: ${key} = ${value}`);
                    }
                    
                    log('Starting fetch request...');
                    fetch('http://127.0.0.1:5000/scrape', {
                        method: 'POST',
                        body: formData,
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    })
                    .then(response => {
                        log(`Response received: ${response.status} ${response.statusText}`);
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        log(`JSON data received: ${JSON.stringify(data)}`);
                        
                        if (data.status === 'success') {
                            log('Success response received, calling showProgressSection()');
                            showProgressSection();
                        } else {
                            log(`Error response: ${data.message}`);
                            alert('Error: ' + data.message);
                            resetToForm();
                        }
                    })
                    .catch(error => {
                        log(`Fetch error: ${error.message}`);
                        alert('Error: ' + error.message);
                        resetToForm();
                    });
                });
            } else {
                log('ERROR: Form not found!');
            }
        });
    </script>
</body>
</html>
