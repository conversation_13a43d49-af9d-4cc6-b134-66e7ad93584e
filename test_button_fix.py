#!/usr/bin/env python3
"""
Quick test to verify the button fix is working.
"""

import requests
import json

def reset_status():
    """Reset the scraping status."""
    try:
        response = requests.post('http://127.0.0.1:5000/api/scraping_reset')
        return response.status_code == 200
    except:
        return False

def test_button_fix():
    """Test that the form submission now works."""
    print("🔧 Testing Button Fix")
    print("=" * 30)
    
    # Reset first
    print("🔄 Resetting status...")
    if not reset_status():
        print("❌ Could not reset status")
        return False
    print("✅ Status reset")
    
    # Test AJAX submission
    try:
        url = 'http://127.0.0.1:5000/scrape'
        data = {
            'keywords': 'button test',
            'class_name': 'button_test',
            'max_images': '3'
        }
        headers = {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/x-www-form-urlencoded'
        }
        
        print("🧪 Testing AJAX form submission...")
        response = requests.post(url, data=data, headers=headers)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            json_data = response.json()
            print(f"Response: {json.dumps(json_data, indent=2)}")
            
            if json_data.get('status') == 'success':
                print("🎉 BUTTON FIX SUCCESSFUL! Form submission working!")
                return True
            else:
                print(f"❌ Form submission failed: {json_data.get('message')}")
                return False
        else:
            print(f"❌ HTTP error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing form: {e}")
        return False

if __name__ == '__main__':
    success = test_button_fix()
    if success:
        print("\n🎊 The Start Scraping button is now working!")
        print("✅ Users can now submit the form successfully")
        print("✅ AJAX integration is functional")
    else:
        print("\n❌ Button still not working - needs more debugging")
    
    exit(0 if success else 1)
