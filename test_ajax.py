#!/usr/bin/env python3
"""
Quick test to verify AJAX functionality is working.
"""

import requests
import json

def test_ajax_submission():
    """Test AJAX form submission."""
    url = 'http://127.0.0.1:5000/scrape'
    
    # Test data
    data = {
        'keywords': 'test cats',
        'class_name': 'test_class',
        'max_images': '10'
    }
    
    # Headers to simulate AJAX request
    headers = {
        'X-Requested-With': 'XMLHttpRequest',
        'Content-Type': 'application/x-www-form-urlencoded'
    }
    
    try:
        print("Testing AJAX form submission...")
        response = requests.post(url, data=data, headers=headers)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        print(f"Response Content: {response.text}")
        
        if response.status_code == 200:
            try:
                json_data = response.json()
                print(f"JSON Response: {json_data}")
                if json_data.get('status') == 'success':
                    print("✅ AJAX submission working correctly!")
                else:
                    print(f"❌ AJAX submission failed: {json_data.get('message')}")
            except json.JSONDecodeError:
                print("❌ Response is not valid JSON")
        else:
            print(f"❌ HTTP error: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to Flask app. Make sure it's running on http://127.0.0.1:5000")
    except Exception as e:
        print(f"❌ Error: {e}")

def test_regular_submission():
    """Test regular form submission."""
    url = 'http://127.0.0.1:5000/scrape'
    
    # Test data
    data = {
        'keywords': 'test dogs',
        'class_name': 'test_class',
        'max_images': '10'
    }
    
    try:
        print("\nTesting regular form submission...")
        response = requests.post(url, data=data, allow_redirects=False)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 302:
            print(f"✅ Regular submission working - redirects to: {response.headers.get('Location')}")
        else:
            print(f"❌ Expected redirect (302), got: {response.status_code}")
            print(f"Response Content: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to Flask app. Make sure it's running on http://127.0.0.1:5000")
    except Exception as e:
        print(f"❌ Error: {e}")

def test_validation_error():
    """Test validation error handling."""
    url = 'http://127.0.0.1:5000/scrape'
    
    # Test data with missing keywords
    data = {
        'keywords': '',  # Empty keywords should trigger validation error
        'class_name': 'test_class',
        'max_images': '10'
    }
    
    # Headers to simulate AJAX request
    headers = {
        'X-Requested-With': 'XMLHttpRequest',
        'Content-Type': 'application/x-www-form-urlencoded'
    }
    
    try:
        print("\nTesting validation error handling...")
        response = requests.post(url, data=data, headers=headers)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Content: {response.text}")
        
        if response.status_code == 200:
            try:
                json_data = response.json()
                print(f"JSON Response: {json_data}")
                if json_data.get('status') == 'error' and 'keywords' in json_data.get('message', '').lower():
                    print("✅ Validation error handling working correctly!")
                else:
                    print(f"❌ Unexpected validation response: {json_data}")
            except json.JSONDecodeError:
                print("❌ Response is not valid JSON")
        else:
            print(f"❌ HTTP error: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to Flask app. Make sure it's running on http://127.0.0.1:5000")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == '__main__':
    print("Testing AJAX integration...")
    test_ajax_submission()
    test_regular_submission()
    test_validation_error()
    print("\nTest completed!")
