#!/usr/bin/env python3
"""
Test the new button-based approach.
"""

import requests
import json

def reset_status():
    """Reset the scraping status."""
    try:
        response = requests.post('http://127.0.0.1:5000/api/scraping_reset')
        return response.status_code == 200
    except:
        return False

def test_new_approach():
    """Test the new button-based approach."""
    print("🧪 Testing New Button-Based Approach")
    print("=" * 40)
    
    # Reset first
    print("🔄 Resetting status...")
    if not reset_status():
        print("❌ Could not reset status")
        return False
    print("✅ Status reset")
    
    # Test the new approach
    try:
        url = 'http://127.0.0.1:5000/scrape'
        
        # Use URLSearchParams format like the new JavaScript
        data = {
            'keywords': 'new approach test',
            'class_name': 'new_approach_test',
            'destination_folder': '',
            'max_images': '5'
        }
        
        headers = {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/x-www-form-urlencoded'
        }
        
        print("🚀 Testing new button approach...")
        print(f"Data: {data}")
        
        response = requests.post(url, data=data, headers=headers)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            json_data = response.json()
            
            if json_data.get('status') == 'success':
                print("✅ NEW APPROACH WORKING!")
                print(f"   Message: {json_data.get('message')}")
                return True
            else:
                print(f"❌ Request failed: {json_data.get('message')}")
                return False
        else:
            print(f"❌ HTTP error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing new approach: {e}")
        return False

def test_validation():
    """Test validation with empty fields."""
    print("\n🧪 Testing Validation")
    print("=" * 25)
    
    try:
        url = 'http://127.0.0.1:5000/scrape'
        
        # Test with empty keywords
        data = {
            'keywords': '',  # Empty keywords
            'class_name': 'test',
            'destination_folder': '',
            'max_images': '5'
        }
        
        headers = {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/x-www-form-urlencoded'
        }
        
        response = requests.post(url, data=data, headers=headers)
        
        if response.status_code == 200:
            json_data = response.json()
            
            if json_data.get('status') == 'error' and 'keywords' in json_data.get('message', '').lower():
                print("✅ Validation working correctly")
                print(f"   Error message: {json_data.get('message')}")
                return True
            else:
                print(f"❌ Unexpected validation response: {json_data}")
                return False
        else:
            print(f"❌ HTTP error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing validation: {e}")
        return False

def test_page_loads():
    """Test that the page loads with new structure."""
    print("\n🌐 Testing Page Structure")
    print("=" * 30)
    
    try:
        response = requests.get('http://127.0.0.1:5000/')
        
        if response.status_code == 200:
            content = response.text
            
            # Check for new elements
            has_start_btn = 'id="startScrapingBtn"' in content
            has_onclick = 'onclick="startScraping()"' in content
            has_function = 'function startScraping()' in content
            has_validation = 'function validateInputs()' in content
            
            print(f"✅ Page loads: {response.status_code}")
            print(f"✅ Start button: {has_start_btn}")
            print(f"✅ Click handler: {has_onclick}")
            print(f"✅ Start function: {has_function}")
            print(f"✅ Validation function: {has_validation}")
            
            return has_start_btn and has_onclick and has_function and has_validation
        else:
            print(f"❌ Page failed to load: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing page: {e}")
        return False

def run_comprehensive_test():
    """Run all tests for the new approach."""
    tests = [
        ("Page Structure", test_page_loads),
        ("Button Approach", test_new_approach),
        ("Validation", test_validation),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with error: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 40)
    print("📊 TEST SUMMARY")
    print("=" * 40)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
    
    print(f"\n🎯 Overall Result: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 NEW BUTTON APPROACH IS WORKING PERFECTLY!")
        print("✅ Direct button click handling")
        print("✅ Proper data collection")
        print("✅ Clean validation")
        print("✅ Better error handling")
        return True
    else:
        print("⚠️  Some tests failed. Check the implementation.")
        return False

if __name__ == '__main__':
    success = run_comprehensive_test()
    exit(0 if success else 1)
