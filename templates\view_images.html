{% extends "base.html" %}

{% block title %}{{ class_name }} Images - Image Scraper{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2>
            <i class="fas fa-images me-2"></i>{{ class_name }} Images
            <span class="badge bg-secondary ms-2">{{ images|length }} images</span>
        </h2>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">Dashboard</a></li>
                <li class="breadcrumb-item active">{{ class_name }}</li>
            </ol>
        </nav>
    </div>
    <div>
        <a href="{{ url_for('dashboard') }}" class="btn btn-outline-secondary me-2">
            <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
        </a>
        <a href="{{ url_for('index') }}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i>Scrape More
        </a>
    </div>
</div>

{% if images %}
    <div class="row g-3" id="imageGrid">
        {% for image in images %}
            <div class="col-sm-6 col-md-4 col-lg-3" id="image-{{ loop.index }}">
                <div class="card h-100 shadow-sm image-card">
                    <div class="position-relative">
                        <img src="{{ url_for('serve_image', filename=image.relative_path) }}" 
                             class="card-img-top" 
                             alt="{{ image.filename }}"
                             style="height: 200px; object-fit: cover;"
                             loading="lazy">
                        <div class="position-absolute top-0 end-0 p-2">
                            <button class="btn btn-danger btn-sm delete-btn" 
                                    data-bs-toggle="modal" 
                                    data-bs-target="#deleteModal"
                                    data-filename="{{ image.filename }}"
                                    data-class="{{ class_name }}">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body p-2">
                        <p class="card-text small text-muted mb-0" title="{{ image.filename }}">
                            {{ image.filename[:30] }}{% if image.filename|length > 30 %}...{% endif %}
                        </p>
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-exclamation-triangle text-warning me-2"></i>Confirm Delete
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete this image?</p>
                    <p class="text-muted small mb-0">This action cannot be undone.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <form method="POST" id="deleteForm" style="display: inline;">
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash me-1"></i>Delete Image
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
{% else %}
    <div class="text-center py-5">
        <div class="mb-4">
            <i class="fas fa-image fa-5x text-muted"></i>
        </div>
        <h3 class="text-muted">No Images Found</h3>
        <p class="text-muted mb-4">This class doesn't contain any images yet.</p>
        <a href="{{ url_for('index') }}" class="btn btn-primary">
            <i class="fas fa-search me-2"></i>Scrape Images for {{ class_name }}
        </a>
    </div>
{% endif %}
{% endblock %}

{% block scripts %}
<script>
// Handle delete button clicks
document.addEventListener('DOMContentLoaded', function() {
    const deleteButtons = document.querySelectorAll('.delete-btn');
    const deleteForm = document.getElementById('deleteForm');
    
    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const filename = this.getAttribute('data-filename');
            const className = this.getAttribute('data-class');
            
            if (deleteForm) {
                deleteForm.action = `/delete_image/${className}/${filename}`;
            }
        });
    });
    
    // Add hover effects to image cards
    const imageCards = document.querySelectorAll('.image-card');
    imageCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.transition = 'transform 0.2s ease';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
});
</script>
{% endblock %}
