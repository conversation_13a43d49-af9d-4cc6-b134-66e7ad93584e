#!/usr/bin/env python3
"""
Reset scraping status to allow new tests.
"""

import requests
import json

def reset_scraping_status():
    """Reset the scraping status by calling the clear logs endpoint."""
    try:
        # Clear logs first
        response = requests.post('http://127.0.0.1:5000/api/scraping_logs/clear')
        if response.status_code == 200:
            print("✅ Logs cleared successfully")
        else:
            print(f"❌ Failed to clear logs: {response.status_code}")
        
        # Check current status
        response = requests.get('http://127.0.0.1:5000/api/scraping_status')
        if response.status_code == 200:
            data = response.json()
            print(f"Current status: {json.dumps(data, indent=2)}")
        else:
            print(f"❌ Failed to get status: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to Flask app. Make sure it's running on http://127.0.0.1:5000")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == '__main__':
    print("Resetting scraping status...")
    reset_scraping_status()
    print("Done!")
