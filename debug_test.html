<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Debug Test Page</h1>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Test AJAX Form Submission</h5>
                    </div>
                    <div class="card-body">
                        <form id="testForm">
                            <div class="mb-3">
                                <label for="keywords" class="form-label">Keywords</label>
                                <input type="text" class="form-control" id="keywords" name="keywords" value="test cats">
                            </div>
                            <div class="mb-3">
                                <label for="class_name" class="form-label">Class Name</label>
                                <input type="text" class="form-control" id="class_name" name="class_name" value="test_class">
                            </div>
                            <div class="mb-3">
                                <label for="max_images" class="form-label">Max Images</label>
                                <select class="form-select" id="max_images" name="max_images">
                                    <option value="10" selected>10 images</option>
                                    <option value="20">20 images</option>
                                </select>
                            </div>
                            <button type="submit" class="btn btn-primary" id="submitBtn">Test Submit</button>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Debug Output</h5>
                    </div>
                    <div class="card-body">
                        <div id="debugOutput" style="height: 300px; overflow-y: auto; background: #f8f9fa; padding: 10px; font-family: monospace; font-size: 12px;">
                            Ready for testing...<br>
                        </div>
                        <button class="btn btn-secondary btn-sm mt-2" onclick="clearDebug()">Clear</button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Test Progress Section Toggle</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-info" onclick="testShowProgress()">Show Progress Section</button>
                        <button class="btn btn-warning" onclick="testHideProgress()">Hide Progress Section</button>
                        <button class="btn btn-success" onclick="testResetForm()">Reset to Form</button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Hidden progress section for testing -->
        <div id="progressSection" style="display: none;" class="mt-4">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5>Progress Section (Test)</h5>
                </div>
                <div class="card-body">
                    <div class="progress mb-3">
                        <div class="progress-bar" id="progressBar" style="width: 0%">0%</div>
                    </div>
                    <div class="alert alert-info" id="statusMessage">
                        <span id="statusText">Test status...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function log(message) {
            const output = document.getElementById('debugOutput');
            const timestamp = new Date().toLocaleTimeString();
            output.innerHTML += `[${timestamp}] ${message}<br>`;
            output.scrollTop = output.scrollHeight;
        }
        
        function clearDebug() {
            document.getElementById('debugOutput').innerHTML = 'Debug cleared...<br>';
        }
        
        function testShowProgress() {
            document.getElementById('progressSection').style.display = 'block';
            log('Progress section shown');
        }
        
        function testHideProgress() {
            document.getElementById('progressSection').style.display = 'none';
            log('Progress section hidden');
        }
        
        function testResetForm() {
            document.getElementById('testForm').reset();
            document.getElementById('progressSection').style.display = 'none';
            log('Form reset and progress hidden');
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            log('DOM loaded successfully');
            
            const form = document.getElementById('testForm');
            if (form) {
                log('Form found, attaching event listener');
                
                form.addEventListener('submit', function(event) {
                    event.preventDefault();
                    log('Form submit triggered');
                    
                    const submitBtn = document.getElementById('submitBtn');
                    submitBtn.disabled = true;
                    submitBtn.textContent = 'Processing...';
                    
                    const formData = new FormData(form);
                    log('Form data prepared');
                    
                    fetch('http://127.0.0.1:5000/scrape', {
                        method: 'POST',
                        body: formData,
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    })
                    .then(response => {
                        log(`Response status: ${response.status}`);
                        return response.json();
                    })
                    .then(data => {
                        log(`Response data: ${JSON.stringify(data)}`);
                        
                        if (data.status === 'success') {
                            log('✅ AJAX submission successful!');
                            testShowProgress();
                        } else {
                            log(`❌ Error: ${data.message}`);
                        }
                        
                        // Re-enable button
                        submitBtn.disabled = false;
                        submitBtn.textContent = 'Test Submit';
                    })
                    .catch(error => {
                        log(`❌ Fetch error: ${error.message}`);
                        submitBtn.disabled = false;
                        submitBtn.textContent = 'Test Submit';
                    });
                });
            } else {
                log('❌ Form not found!');
            }
        });
    </script>
</body>
</html>
