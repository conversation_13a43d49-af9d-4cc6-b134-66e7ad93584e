#!/usr/bin/env python3
"""
Test AJAX directly to see what's happening.
"""

import requests
import json

def test_ajax_direct():
    """Test AJAX request directly."""
    print("🧪 Testing AJAX Request Directly")
    print("=" * 40)
    
    # Reset first
    try:
        reset_response = requests.post('http://127.0.0.1:5000/api/scraping_reset')
        print(f"Reset status: {reset_response.status_code}")
    except:
        print("Could not reset - continuing anyway")
    
    # Test AJAX request
    url = 'http://127.0.0.1:5000/scrape'
    data = {
        'keywords': 'ajax test',
        'class_name': 'ajax_test',
        'max_images': '3'
    }
    
    headers = {
        'X-Requested-With': 'XMLHttpRequest',
        'Content-Type': 'application/x-www-form-urlencoded'
    }
    
    try:
        print(f"Making request to: {url}")
        print(f"Data: {data}")
        print(f"Headers: {headers}")
        
        response = requests.post(url, data=data, headers=headers)
        
        print(f"\nResponse Status: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        print(f"Response Content-Type: {response.headers.get('Content-Type', 'Not set')}")
        print(f"Response Text: {response.text}")
        
        if response.headers.get('Content-Type', '').startswith('application/json'):
            try:
                json_data = response.json()
                print(f"JSON Data: {json.dumps(json_data, indent=2)}")
                
                if json_data.get('status') == 'success':
                    print("✅ AJAX request successful!")
                    return True
                else:
                    print(f"❌ AJAX request failed: {json_data.get('message')}")
                    return False
            except json.JSONDecodeError as e:
                print(f"❌ JSON decode error: {e}")
                return False
        else:
            print("❌ Response is not JSON")
            print("This might be the issue - Flask is returning HTML instead of JSON")
            return False
            
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return False

def test_regular_request():
    """Test regular (non-AJAX) request for comparison."""
    print("\n🧪 Testing Regular Request for Comparison")
    print("=" * 50)
    
    url = 'http://127.0.0.1:5000/scrape'
    data = {
        'keywords': 'regular test',
        'class_name': 'regular_test',
        'max_images': '3'
    }
    
    try:
        response = requests.post(url, data=data, allow_redirects=False)
        
        print(f"Response Status: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        print(f"Response Text (first 200 chars): {response.text[:200]}...")
        
        if response.status_code == 302:
            print("✅ Regular request redirects as expected")
            return True
        else:
            print("❌ Regular request doesn't redirect")
            return False
            
    except Exception as e:
        print(f"❌ Regular request failed: {e}")
        return False

if __name__ == '__main__':
    print("🔍 Debugging AJAX Issue")
    print("=" * 50)
    
    ajax_success = test_ajax_direct()
    regular_success = test_regular_request()
    
    print("\n📊 Summary:")
    print(f"AJAX Request: {'✅ Working' if ajax_success else '❌ Failed'}")
    print(f"Regular Request: {'✅ Working' if regular_success else '❌ Failed'}")
    
    if not ajax_success:
        print("\n🔧 Possible Issues:")
        print("1. Flask not detecting AJAX header correctly")
        print("2. CORS issue preventing response")
        print("3. Flask returning HTML instead of JSON")
        print("4. JavaScript fetch() not working as expected")
    
    exit(0 if ajax_success else 1)
