#!/usr/bin/env python3
"""
Final comprehensive test of the scraping progress integration.
"""

import requests
import json
import time

def reset_status():
    """Reset the scraping status."""
    try:
        response = requests.post('http://127.0.0.1:5000/api/scraping_reset')
        return response.status_code == 200
    except:
        return False

def test_main_page():
    """Test that the main page loads with both sections."""
    try:
        response = requests.get('http://127.0.0.1:5000/')
        if response.status_code == 200:
            content = response.text
            has_form_section = 'id="formSection"' in content
            has_progress_section = 'id="progressSection"' in content
            has_ajax_script = 'X-Requested-With' in content
            
            print(f"✅ Main page loads: {response.status_code}")
            print(f"✅ Form section present: {has_form_section}")
            print(f"✅ Progress section present: {has_progress_section}")
            print(f"✅ AJAX script present: {has_ajax_script}")
            
            return has_form_section and has_progress_section and has_ajax_script
        else:
            print(f"❌ Main page failed to load: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error testing main page: {e}")
        return False

def test_ajax_submission():
    """Test AJAX form submission."""
    try:
        url = 'http://127.0.0.1:5000/scrape'
        data = {
            'keywords': 'final test',
            'class_name': 'final_test',
            'max_images': '3'
        }
        headers = {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/x-www-form-urlencoded'
        }
        
        response = requests.post(url, data=data, headers=headers)
        
        if response.status_code == 200:
            json_data = response.json()
            success = json_data.get('status') == 'success'
            print(f"✅ AJAX submission: {response.status_code}")
            print(f"✅ Success response: {success}")
            print(f"   Message: {json_data.get('message', 'No message')}")
            return success
        else:
            print(f"❌ AJAX submission failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error testing AJAX submission: {e}")
        return False

def test_traditional_submission():
    """Test traditional form submission (should redirect)."""
    try:
        url = 'http://127.0.0.1:5000/scrape'
        data = {
            'keywords': 'traditional test',
            'class_name': 'traditional_test',
            'max_images': '3'
        }
        
        response = requests.post(url, data=data, allow_redirects=False)
        
        is_redirect = response.status_code == 302
        redirect_location = response.headers.get('Location', '')
        
        print(f"✅ Traditional submission redirect: {is_redirect}")
        print(f"   Redirect location: {redirect_location}")
        
        return is_redirect
    except Exception as e:
        print(f"❌ Error testing traditional submission: {e}")
        return False

def test_api_endpoints():
    """Test that all API endpoints are working."""
    endpoints = [
        '/api/scraping_status',
        '/api/scraping_logs',
    ]
    
    all_working = True
    
    for endpoint in endpoints:
        try:
            response = requests.get(f'http://127.0.0.1:5000{endpoint}')
            working = response.status_code == 200
            print(f"✅ API {endpoint}: {response.status_code}")
            if not working:
                all_working = False
        except Exception as e:
            print(f"❌ API {endpoint} error: {e}")
            all_working = False
    
    return all_working

def test_validation_errors():
    """Test validation error handling."""
    try:
        url = 'http://127.0.0.1:5000/scrape'
        data = {
            'keywords': '',  # Empty keywords should trigger error
            'class_name': 'test',
            'max_images': '5'
        }
        headers = {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/x-www-form-urlencoded'
        }
        
        response = requests.post(url, data=data, headers=headers)
        
        if response.status_code == 200:
            json_data = response.json()
            is_error = json_data.get('status') == 'error'
            has_keywords_msg = 'keywords' in json_data.get('message', '').lower()
            
            print(f"✅ Validation error response: {is_error}")
            print(f"✅ Keywords error message: {has_keywords_msg}")
            print(f"   Error message: {json_data.get('message', 'No message')}")
            
            return is_error and has_keywords_msg
        else:
            print(f"❌ Validation test failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error testing validation: {e}")
        return False

def run_comprehensive_test():
    """Run all tests and provide summary."""
    print("🧪 Running Comprehensive Integration Test")
    print("=" * 50)
    
    # Reset status first
    print("\n🔄 Resetting scraping status...")
    if not reset_status():
        print("❌ Could not reset status - tests may be unreliable")
    else:
        print("✅ Status reset successfully")
    
    time.sleep(1)  # Give it a moment
    
    # Run all tests
    tests = [
        ("Main Page Loading", test_main_page),
        ("AJAX Form Submission", test_ajax_submission),
        ("Traditional Form Submission", test_traditional_submission),
        ("API Endpoints", test_api_endpoints),
        ("Validation Error Handling", test_validation_errors),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 Testing: {test_name}")
        print("-" * 30)
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
            results.append((test_name, False))
        
        time.sleep(1)  # Brief pause between tests
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
    
    print(f"\n🎯 Overall Result: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! Integration is working perfectly!")
        return True
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
        return False

if __name__ == '__main__':
    success = run_comprehensive_test()
    exit(0 if success else 1)
