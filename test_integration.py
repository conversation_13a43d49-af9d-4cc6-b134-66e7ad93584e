#!/usr/bin/env python3
"""
Test script to verify the integration of scraping progress into the main form page.
"""

import unittest
import json
from unittest.mock import patch, MagicMock
from app import app, scraping_status


class TestScrapingIntegration(unittest.TestCase):
    """Test cases for the integrated scraping functionality."""

    def setUp(self):
        """Set up test client and reset scraping status."""
        self.app = app.test_client()
        self.app.testing = True
        # Reset scraping status
        scraping_status['is_running'] = False
        scraping_status['progress'] = ''
        scraping_status['current_task'] = None

    def test_index_page_loads(self):
        """Test that the main index page loads successfully."""
        response = self.app.get('/')
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'Scrape Images from Google', response.data)
        self.assertIn(b'progressSection', response.data)  # Check progress section exists
        self.assertIn(b'formSection', response.data)  # Check form section exists

    def test_ajax_form_submission_success(self):
        """Test AJAX form submission returns JSON response."""
        with patch('threading.Thread') as mock_thread:
            mock_thread.return_value.start = MagicMock()
            
            response = self.app.post('/scrape', 
                data={
                    'keywords': 'test cats',
                    'class_name': 'test_class',
                    'max_images': '10'
                },
                headers={'X-Requested-With': 'XMLHttpRequest'}
            )
            
            self.assertEqual(response.status_code, 200)
            data = json.loads(response.data)
            self.assertEqual(data['status'], 'success')
            self.assertIn('scraping started', data['message'].lower())

    def test_ajax_form_submission_validation_error(self):
        """Test AJAX form submission with validation errors."""
        response = self.app.post('/scrape', 
            data={
                'keywords': '',  # Empty keywords should cause validation error
                'class_name': 'test_class',
                'max_images': '10'
            },
            headers={'X-Requested-With': 'XMLHttpRequest'}
        )
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertEqual(data['status'], 'error')
        self.assertIn('keywords', data['message'].lower())

    def test_traditional_form_submission_still_works(self):
        """Test that traditional form submission still redirects properly."""
        with patch('threading.Thread') as mock_thread:
            mock_thread.return_value.start = MagicMock()
            
            response = self.app.post('/scrape', 
                data={
                    'keywords': 'test dogs',
                    'class_name': 'test_class',
                    'max_images': '10'
                },
                follow_redirects=False
            )
            
            self.assertEqual(response.status_code, 302)  # Redirect response
            self.assertIn('/scraping_progress', response.location)

    def test_scraping_already_running_ajax(self):
        """Test AJAX response when scraping is already running."""
        scraping_status['is_running'] = True
        
        response = self.app.post('/scrape', 
            data={
                'keywords': 'test birds',
                'class_name': 'test_class',
                'max_images': '10'
            },
            headers={'X-Requested-With': 'XMLHttpRequest'}
        )
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertEqual(data['status'], 'error')
        self.assertIn('already in progress', data['message'].lower())

    def test_api_endpoints_still_work(self):
        """Test that existing API endpoints still function."""
        # Test scraping status endpoint
        response = self.app.get('/api/scraping_status')
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertIn('is_running', data)
        
        # Test logs endpoint
        response = self.app.get('/api/scraping_logs')
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertIn('logs', data)

    def test_progress_page_still_accessible(self):
        """Test that the original progress page is still accessible."""
        response = self.app.get('/scraping_progress')
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'Scraping in Progress', response.data)


if __name__ == '__main__':
    print("Running integration tests for scraping progress integration...")
    unittest.main(verbosity=2)
